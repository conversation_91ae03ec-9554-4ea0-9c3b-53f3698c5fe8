# supplierLaborNew.vue 页面修改总结

## 修改目标
将 `supplierLaborNew.vue` 页面改造为支持多个业务场景的统一页面：
1. 新增人员
2. 查看人员详情
3. 编辑人员信息

## 主要修改内容

### 1. 页面模式控制
- 添加了 `mode` 数据属性，支持三种模式：'new'、'view'、'edit'
- 添加了 `isEditable` 计算属性，控制表单字段的可编辑状态
- 根据路由参数判断页面模式：有 id 参数时为查看模式，无 id 参数时为新增模式

### 2. 表单字段扩展
在原有基础上添加了详情页面需要的字段：
- 年龄 (age)
- 性别 (gender)
- 证件有效期 (idCardPeriod)
- 民族 (nation)
- 详细地址 (address)

### 3. 编辑模式控制
- 在查看模式下，页面右上角显示"编辑"按钮
- 点击编辑按钮切换到编辑模式
- 编辑模式下显示"取消"和"确定"按钮
- 支持取消编辑时恢复原始数据

### 4. 字段编辑权限控制
根据 supplierLaborDetail.vue 的设计，以下字段在编辑模式下的权限：
- **可编辑字段**：姓名、证件有效期、民族、详细地址、银行卡号、开户行
- **不可编辑字段**：身份证件号码、手机号、年龄、性别（设置为 disabled）

### 5. 项目经历功能增强
- 添加按钮和删除按钮仅在编辑模式下显示
- 表单字段在查看模式下为只读状态
- 支持项目经历数据的返显和编辑

### 6. 附件信息功能增强
- 修改了 FileUploadComponent 组件，添加 readonly 属性支持
- 在只读模式下隐藏上传按钮和删除按钮
- 支持附件信息的返显

### 7. 数据加载和保存
- 添加了 `loadLaborData()` 方法，用于加载人员详情数据
- 添加了 `populateFormData()` 方法，用于填充表单数据
- 修改了数据提交逻辑，支持新增和编辑两种模式
- 使用 `/api/supplier/labor/getDetail/{id}` 接口获取详情数据
- 使用 `/api/supplier/labor/update` 接口更新人员信息

### 8. 路由配置修改
- 修改了 `src/platform/routes.js`，将 `/supplierLabor/:id` 路由指向 `SupplierLaborNew` 组件
- 这样实现了用一个组件处理多种业务场景

### 9. 样式调整
- 添加了编辑按钮的样式
- 调整了页面布局，支持编辑按钮的显示

## 接口数据格式
根据提供的接口返回示例，数据结构包括：
- `basicInfo`: 基本信息
- `projectHistory`: 项目经历数组
- `photos`: 附件信息数组
- `employeeInfo`: 员工信息数组
- `laborBankCard`: 银行卡信息

## 使用方式
1. **新增人员**：访问 `/supplierLabor/new`
2. **查看详情**：访问 `/supplierLabor/{id}`，页面默认为查看模式
3. **编辑信息**：在查看模式下点击右上角的"编辑"按钮

## 注意事项
1. 新增模式下仍然显示作业主体、客户、合同选择字段
2. 查看/编辑模式下隐藏这些选择字段，因为详情页面不需要显示
3. 保持了原有的表单验证规则
4. 支持项目经历和附件信息的完整功能

## 废弃的文件
- `supplierLaborDetail.vue` 可以废弃，其功能已整合到 `supplierLaborNew.vue` 中

这样的设计实现了一个页面支持多种业务场景，提高了代码复用性和维护性。
