# supplierLaborNew.vue 功能测试指南

## 测试环境
- 开发服务器：http://localhost:5174/
- 测试页面路径：
  - 新增人员：`/supplierLabor/new`
  - 查看详情：`/supplierLabor/{id}` (需要真实的人员ID)

## 测试场景

### 1. 新增人员功能测试
**访问路径**：`/supplierLabor/new`

**预期行为**：
- 页面标题显示"添加人员"
- 显示所有基本信息字段（姓名、身份证、手机号、年龄、性别等）
- 显示作业主体、客户、合同选择字段
- 项目经历部分显示"添加"按钮
- 附件信息显示上传功能
- 底部显示"取消"和"确定"按钮
- 所有字段都可以编辑

**测试步骤**：
1. 填写基本信息
2. 选择作业主体、客户、合同
3. 添加项目经历
4. 上传附件
5. 点击确定提交

### 2. 查看详情功能测试
**访问路径**：`/supplierLabor/{id}` (需要替换为真实ID)

**预期行为**：
- 页面右上角显示"编辑"按钮
- 页面标题显示"基本信息"
- 显示所有基本信息字段，但为只读状态
- 不显示作业主体、客户、合同选择字段
- 项目经历部分不显示"添加"和删除按钮
- 附件信息不显示上传按钮和删除按钮
- 底部不显示操作按钮
- 所有字段都不可编辑

**测试步骤**：
1. 访问详情页面
2. 验证数据正确显示
3. 验证所有字段为只读状态
4. 验证编辑按钮存在

### 3. 编辑功能测试
**前置条件**：在查看详情页面

**预期行为**：
- 点击"编辑"按钮后，页面切换到编辑模式
- 可编辑字段变为可输入状态：姓名、证件有效期、民族、详细地址、银行卡号、开户行
- 不可编辑字段保持禁用状态：身份证件号码、手机号、年龄、性别
- 项目经历部分显示"添加"按钮和删除按钮
- 附件信息显示上传按钮和删除按钮
- 底部显示"取消"和"确定"按钮

**测试步骤**：
1. 在详情页面点击"编辑"按钮
2. 修改可编辑字段
3. 添加/删除项目经历
4. 上传/删除附件
5. 点击"确定"保存修改
6. 验证数据更新成功，页面回到查看模式

### 4. 取消编辑功能测试
**前置条件**：在编辑模式

**预期行为**：
- 点击"取消"按钮后，页面回到查看模式
- 所有修改的数据恢复到编辑前的状态
- 表单验证错误清除

**测试步骤**：
1. 在编辑模式下修改一些字段
2. 点击"取消"按钮
3. 验证数据恢复到原始状态
4. 验证页面回到查看模式

## 数据验证测试

### 1. 新增模式验证
- 姓名：必填，最大20字符
- 身份证：必填，18位格式验证
- 手机号：必填，11位格式验证
- 银行卡号：可选，12-20位数字验证
- 开户行：可选，最大64字符
- 作业主体：必填
- 客户：必填
- 合同：必填

### 2. 编辑模式验证
- 姓名：必填，最大20字符
- 银行卡号：可选，12-20位数字验证
- 开户行：可选，最大64字符
- 作业主体、客户、合同：不验证（不显示）

## 接口调用测试

### 1. 获取详情接口
- 接口：`GET /api/supplier/labor/getDetail/{id}`
- 验证返回数据格式是否正确
- 验证数据是否正确填充到表单

### 2. 更新接口
- 接口：`POST /api/supplier/labor/update`
- 验证请求数据格式是否正确
- 验证更新成功后的响应处理

### 3. 新增接口
- 接口：`POST /api/supplier/labor/create`
- 验证请求数据格式是否正确
- 验证新增成功后的跳转

## 常见问题排查

1. **页面无法加载**：检查路由配置是否正确
2. **数据不显示**：检查接口返回数据格式和字段映射
3. **编辑按钮不显示**：检查页面模式判断逻辑
4. **表单验证异常**：检查动态验证规则配置
5. **文件上传问题**：检查 FileUploadComponent 的 readonly 属性

## 注意事项
- 测试时需要确保有有效的人员ID用于详情页面测试
- 需要确保后端接口正常运行
- 文件上传功能需要确保上传服务正常
